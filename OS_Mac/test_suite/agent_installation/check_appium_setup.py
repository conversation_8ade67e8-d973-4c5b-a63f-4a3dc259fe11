#!/usr/bin/env python3
"""
Check Appium setup and provide installation instructions
"""

import subprocess
import sys
import os

def check_appium_python_client():
    """Check if Appium Python client is installed"""
    try:
        import appium
        print("✅ Appium Python client is installed")
        print(f"   Version: {appium.__version__}")
        return True
    except ImportError:
        print("❌ Appium Python client not installed")
        print("   Install with: pip install Appium-Python-Client")
        return False

def check_appium_server():
    """Check if Appium server is running"""
    try:
        import requests
        response = requests.get("http://localhost:4723/wd/hub/status", timeout=5)
        if response.status_code == 200:
            print("✅ Appium server is running on localhost:4723")
            return True
        else:
            print("❌ Appium server not responding properly")
            return False
    except Exception as e:
        print("❌ Appium server not running on localhost:4723")
        print(f"   Error: {e}")
        return False

def check_node_and_npm():
    """Check if Node.js and npm are installed"""
    try:
        node_result = subprocess.run(["node", "--version"], capture_output=True, text=True)
        if node_result.returncode == 0:
            print(f"✅ Node.js installed: {node_result.stdout.strip()}")
            node_ok = True
        else:
            print("❌ Node.js not found")
            node_ok = False
    except FileNotFoundError:
        print("❌ Node.js not found")
        node_ok = False
    
    try:
        npm_result = subprocess.run(["npm", "--version"], capture_output=True, text=True)
        if npm_result.returncode == 0:
            print(f"✅ npm installed: {npm_result.stdout.strip()}")
            npm_ok = True
        else:
            print("❌ npm not found")
            npm_ok = False
    except FileNotFoundError:
        print("❌ npm not found")
        npm_ok = False
    
    return node_ok and npm_ok

def check_appium_cli():
    """Check if Appium CLI is installed"""
    try:
        result = subprocess.run(["appium", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Appium CLI installed: {result.stdout.strip()}")
            return True
        else:
            print("❌ Appium CLI not found")
            return False
    except FileNotFoundError:
        print("❌ Appium CLI not found")
        return False

def check_appium_mac2_driver():
    """Check if Appium Mac2 driver is installed"""
    try:
        result = subprocess.run(["appium", "driver", "list"], capture_output=True, text=True)
        if result.returncode == 0 and "mac2" in result.stdout.lower():
            print("✅ Appium Mac2 driver is installed")
            return True
        else:
            print("❌ Appium Mac2 driver not found")
            return False
    except FileNotFoundError:
        print("❌ Cannot check Appium drivers (Appium CLI not found)")
        return False

def provide_installation_instructions():
    """Provide step-by-step installation instructions"""
    print("\n" + "="*80)
    print("📋 APPIUM SETUP INSTRUCTIONS FOR macOS UI AUTOMATION")
    print("="*80)
    
    print("\n1️⃣  Install Node.js and npm:")
    print("   • Download from: https://nodejs.org/")
    print("   • Or use Homebrew: brew install node")
    
    print("\n2️⃣  Install Appium CLI globally:")
    print("   npm install -g appium")
    
    print("\n3️⃣  Install Appium Mac2 driver:")
    print("   appium driver install mac2")
    
    print("\n4️⃣  Install Appium Python client:")
    print("   pip install Appium-Python-Client")
    
    print("\n5️⃣  Start Appium server:")
    print("   appium --allow-insecure chromedriver_autodownload")
    print("   (Run this in a separate terminal)")
    
    print("\n6️⃣  Enable macOS accessibility permissions:")
    print("   • Go to System Preferences > Security & Privacy > Privacy")
    print("   • Select 'Accessibility' from the left panel")
    print("   • Add Terminal and your IDE to the list")
    print("   • Make sure they are checked/enabled")
    
    print("\n7️⃣  Test the setup:")
    print("   python3 check_appium_setup.py")

def main():
    print("🔍 CHECKING APPIUM SETUP FOR macOS UI AUTOMATION")
    print("="*60)
    
    checks = [
        ("Node.js and npm", check_node_and_npm),
        ("Appium CLI", check_appium_cli),
        ("Appium Mac2 driver", check_appium_mac2_driver),
        ("Appium Python client", check_appium_python_client),
        ("Appium server", check_appium_server),
    ]
    
    results = {}
    for name, check_func in checks:
        print(f"\n🔍 Checking {name}...")
        results[name] = check_func()
    
    # Summary
    print("\n" + "="*60)
    print("📊 SETUP SUMMARY")
    print("="*60)
    
    passed = 0
    for name, result in results.items():
        status = "✅ OK" if result else "❌ MISSING"
        print(f"   {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Setup Status: {passed}/{len(results)} components ready")
    
    if passed == len(results):
        print("🎉 Appium setup is complete! You can now run UI automation tests.")
    else:
        print("⚠️  Appium setup is incomplete. See installation instructions below.")
        provide_installation_instructions()
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
################################################################
#               UI AUTOMATION TEST FOR DMG INSTALLATION       #
#                                                              #
# AUTHOR : ZTB Automation Framework                           #
################################################################

"""
UI automation test for DMG installation using Selenium and macOS accessibility.
This test automates the installer GUI interaction.
"""

import os
import sys
import time
import subprocess
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from OS_Mac.library.agent_installation import ZTBInstaller
from shared_utils.common.logger import Logger

try:
    from appium import webdriver as appium_webdriver
    from appium.webdriver.common.appiumby import AppiumBy
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
    APPIUM_AVAILABLE = True
except ImportError:
    APPIUM_AVAILABLE = False

try:
    import pyautogui
    import pygetwindow as gw
    PYAUTOGUI_AVAILABLE = True
except ImportError:
    PYAUTOGUI_AVAILABLE = False


class MacOSInstallerUIAutomation:
    """
    UI automation class for macOS installer using Appium and fallback methods
    """

    def __init__(self, logger=None):
        self.logger = logger or Logger.initialize_logger("ui_automation.log", log_level="DEBUG")
        self.driver = None

        # Configure PyAutoGUI as fallback
        if PYAUTOGUI_AVAILABLE:
            pyautogui.FAILSAFE = True
            pyautogui.PAUSE = 2  # 2 second pause between actions for stability

    def setup_appium_driver(self):
        """
        Setup Appium driver for macOS automation
        """
        try:
            if not APPIUM_AVAILABLE:
                self.logger.warning("Appium not available, using fallback methods")
                return False

            # Desired capabilities for macOS automation
            desired_caps = {
                'platformName': 'Mac',
                'automationName': 'Mac2',
                'bundleId': 'com.apple.installer',  # macOS Installer bundle ID
                'newCommandTimeout': 300
            }

            # Connect to Appium server (assumes Appium server is running)
            self.driver = appium_webdriver.Remote('http://localhost:4723/wd/hub', desired_caps)
            self.logger.info("Appium driver initialized successfully")
            return True

        except Exception as e:
            self.logger.warning(f"Failed to setup Appium driver: {e}")
            self.logger.info("Will use fallback automation methods")
            return False

    def teardown_appium_driver(self):
        """
        Cleanup Appium driver
        """
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
                self.logger.info("Appium driver closed")
        except Exception as e:
            self.logger.warning(f"Error closing Appium driver: {e}")

    def find_element_by_text(self, text, timeout=10):
        """
        Find UI element by text using Appium
        """
        try:
            if not self.driver:
                return None

            wait = WebDriverWait(self.driver, timeout)

            # Try different locator strategies
            locators = [
                (AppiumBy.ACCESSIBILITY_ID, text),
                (AppiumBy.NAME, text),
                (AppiumBy.XPATH, f"//*[@title='{text}']"),
                (AppiumBy.XPATH, f"//*[contains(@title, '{text}')]"),
                (AppiumBy.XPATH, f"//*[@label='{text}']"),
                (AppiumBy.XPATH, f"//*[contains(@label, '{text}')]")
            ]

            for by, value in locators:
                try:
                    element = wait.until(EC.presence_of_element_located((by, value)))
                    self.logger.info(f"Found element with text '{text}' using {by}")
                    return element
                except:
                    continue

            return None

        except Exception as e:
            self.logger.error(f"Error finding element by text '{text}': {e}")
            return None

    def click_element_by_text(self, text, timeout=10):
        """
        Click UI element by text using Appium
        """
        try:
            element = self.find_element_by_text(text, timeout)
            if element:
                element.click()
                self.logger.info(f"Clicked element with text: {text}")
                time.sleep(1)
                return True
            else:
                self.logger.warning(f"Element with text '{text}' not found")
                return False

        except Exception as e:
            self.logger.error(f"Error clicking element with text '{text}': {e}")
            return False
        
    def find_installer_window(self, window_title_contains="Installer"):
        """
        Find the installer window using AppleScript
        """
        try:
            # Use AppleScript to find installer windows
            applescript = f'''
            tell application "System Events"
                set installerProcesses to (every process whose name contains "{window_title_contains}")
                if (count of installerProcesses) > 0 then
                    return name of first item of installerProcesses
                else
                    return "not found"
                end if
            end tell
            '''

            result = subprocess.run(
                ["osascript", "-e", applescript],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0 and "not found" not in result.stdout:
                process_name = result.stdout.strip()
                self.logger.info(f"Found installer process: {process_name}")
                return process_name
            else:
                self.logger.warning(f"No installer window found containing '{window_title_contains}'")
                return None

        except Exception as e:
            self.logger.error(f"Error finding installer window: {e}")
            return None
    
    def activate_window(self, process_name):
        """
        Activate and bring window to front using AppleScript
        """
        try:
            if process_name:
                applescript = f'''
                tell application "System Events"
                    tell process "{process_name}"
                        set frontmost to true
                    end tell
                end tell
                '''

                result = subprocess.run(
                    ["osascript", "-e", applescript],
                    capture_output=True,
                    text=True,
                    timeout=10
                )

                if result.returncode == 0:
                    time.sleep(1)
                    self.logger.info(f"Activated process: {process_name}")
                    return True
                else:
                    self.logger.error(f"Failed to activate process: {result.stderr}")
                    return False
            return False
        except Exception as e:
            self.logger.error(f"Error activating window: {e}")
            return False
    
    def click_button_by_applescript(self, button_text, window_name=None, timeout=10):
        """
        Click a button using AppleScript for more reliable interaction
        """
        try:
            self.logger.info(f"Looking for button with text: {button_text}")

            # Build AppleScript to find and click button
            if window_name:
                applescript = f'''
                tell application "System Events"
                    tell process "{window_name}"
                        try
                            click button "{button_text}" of front window
                            return "success"
                        on error
                            return "not found"
                        end try
                    end tell
                end tell
                '''
            else:
                applescript = f'''
                tell application "System Events"
                    set frontApp to name of first application process whose frontmost is true
                    tell process frontApp
                        try
                            click button "{button_text}" of front window
                            return "success"
                        on error
                            return "not found"
                        end try
                    end tell
                end tell
                '''

            result = subprocess.run(
                ["osascript", "-e", applescript],
                capture_output=True,
                text=True,
                timeout=timeout
            )

            if result.returncode == 0 and "success" in result.stdout:
                self.logger.info(f"Successfully clicked button: {button_text}")
                time.sleep(1)
                return True
            else:
                self.logger.warning(f"Button '{button_text}' not found or not clickable")
                return False

        except Exception as e:
            self.logger.error(f"Error clicking button '{button_text}': {e}")
            return False

    def click_button_by_text(self, button_text, timeout=10):
        """
        Click a button by searching for text on screen using PyAutoGUI
        Note: This method is disabled as it requires image files, not text strings
        """
        self.logger.info(f"PyAutoGUI text detection not available for: {button_text} (timeout: {timeout}s)")
        self.logger.info("Use AppleScript method or coordinate-based clicking instead")
        return False
    
    def click_continue_button(self):
        """
        Click the Continue button in the installer
        """
        continue_texts = ["Continue", "Next", "Install", "Agree"]

        # Try Appium first
        for text in continue_texts:
            if self.click_element_by_text(text):
                return True

        # Try AppleScript as fallback
        for text in continue_texts:
            if self.click_button_by_applescript(text):
                return True

        # Try clicking at common button locations as last resort
        try:
            if PYAUTOGUI_AVAILABLE:
                # Common locations for Continue/Next buttons in macOS installers
                common_locations = [
                    (927, 681),  # Bottom right for Agree button
                    (800, 500),  # Bottom right area
                    (700, 450),  # Center right
                ]

                for x, y in common_locations:
                    pyautogui.click(x, y)
                    time.sleep(2)
                    self.logger.info(f"Clicked at location ({x}, {y})")

        except Exception as e:
            self.logger.error(f"Error clicking at common locations: {e}")

        return False
    
    def handle_license_agreement(self):
        """
        Handle license agreement dialog - both DMG and installer license screens
        """
        try:
            self.logger.info("Handling license agreement...")

            # Look for Agree button first with Appium
            agree_texts = ["Agree", "I Agree", "Accept"]

            for text in agree_texts:
                if self.click_element_by_text(text):
                    time.sleep(2)
                    return True

            # Fallback to AppleScript
            for text in agree_texts:
                if self.click_button_by_applescript(text):
                    time.sleep(2)
                    return True

            # Try pressing spacebar to check agreement checkbox
            if PYAUTOGUI_AVAILABLE:
                pyautogui.press('space')
                time.sleep(1)

            # Then try to continue
            return self.click_continue_button()

        except Exception as e:
            self.logger.error(f"Error handling license agreement: {e}")
            return False
    
    def click_pkg_file_in_dmg(self, mount_path):
        """
        Click on the .pkg file in the mounted DMG
        """
        try:
            self.logger.info("Looking for .pkg file in DMG to click...")

            # Find .pkg files
            pkg_files = [f for f in os.listdir(mount_path) if f.endswith(('.pkg', '.mpkg'))]

            if not pkg_files:
                self.logger.error("No .pkg files found in DMG")
                return False

            pkg_file = pkg_files[0]
            pkg_path = os.path.join(mount_path, pkg_file)

            self.logger.info(f"Found .pkg file: {pkg_file}")

            # Open the .pkg file using Finder
            subprocess.run(["open", pkg_path])
            time.sleep(3)  # Wait for installer to launch

            return True

        except Exception as e:
            self.logger.error(f"Error clicking .pkg file: {e}")
            return False

    def handle_browser_selection(self):
        """
        Handle browser selection - select Edge and uncheck Chrome
        """
        try:
            self.logger.info("Handling browser selection...")

            # Look for Edge checkbox/radio button and select it
            edge_keywords = ["Edge", "Microsoft Edge", "edge"]

            for keyword in edge_keywords:
                # Try to find and click Edge option
                if self.click_button_by_applescript(keyword):
                    self.logger.info(f"Selected Edge browser option")
                    break
                elif self.click_button_by_text(keyword):
                    self.logger.info(f"Selected Edge browser option")
                    break

            # Look for Chrome checkbox and uncheck it
            chrome_keywords = ["Chrome", "Google Chrome", "chrome"]

            for keyword in chrome_keywords:
                # Try to find and uncheck Chrome option
                if self.click_button_by_applescript(keyword):
                    self.logger.info(f"Unchecked Chrome browser option")
                    break
                elif self.click_button_by_text(keyword):
                    self.logger.info(f"Unchecked Chrome browser option")
                    break

            time.sleep(2)
            return True

        except Exception as e:
            self.logger.error(f"Error handling browser selection: {e}")
            return False

    def handle_installation_type(self):
        """
        Handle installation type selection and browser preferences
        """
        try:
            self.logger.info("Handling installation type selection...")

            # Handle browser selection if present
            self.handle_browser_selection()

            # Continue with installation
            return self.click_continue_button()

        except Exception as e:
            self.logger.error(f"Error handling installation type: {e}")
            return False
    
    def handle_admin_password(self, password=None):
        """
        Handle admin password prompt
        """
        try:
            self.logger.info("Handling admin password prompt...")
            
            if password:
                # Type the password
                pyautogui.typewrite(password)
                time.sleep(1)
                
                # Press Enter or click Install
                pyautogui.press('enter')
                time.sleep(2)
                
                return True
            else:
                self.logger.warning("No password provided for admin authentication")
                return False
                
        except Exception as e:
            self.logger.error(f"Error handling admin password: {e}")
            return False
    
    def wait_for_installation_complete(self, timeout=300):
        """
        Wait for installation to complete
        """
        try:
            self.logger.info(f"Waiting for installation to complete (timeout: {timeout}s)...")

            start_time = time.time()
            while time.time() - start_time < timeout:
                # Look for completion indicators using AppleScript first
                completion_texts = ["Close", "Done", "Finish", "Summary"]

                for text in completion_texts:
                    if self.click_button_by_applescript(text, timeout=1):
                        self.logger.info(f"Found completion indicator: {text}")
                        return True

                # PyAutoGUI image detection disabled (requires image files)
                # Instead, we rely on AppleScript detection above

                time.sleep(5)  # Check every 5 seconds

            self.logger.warning(f"Installation did not complete within {timeout} seconds")
            return False

        except Exception as e:
            self.logger.error(f"Error waiting for installation completion: {e}")
            return False

    def verify_installation(self):
        """
        Verify that Zscaler Zero Trust Browser - Edge is installed in Applications
        """
        try:
            self.logger.info("Verifying installation...")

            # Check Applications folder for Zscaler Zero Trust Browser - Edge
            applications_path = "/Applications"
            app_names = [
                "Zscaler Zero Trust Browser - Edge.app",
                "Zscaler Zero Trust Browser.app",
                "ZscalerZTB.app"
            ]

            for app_name in app_names:
                app_path = os.path.join(applications_path, app_name)
                if os.path.exists(app_path):
                    self.logger.info(f"✅ Found installed application: {app_name}")
                    return True, app_path

            # List all applications to see what's available
            try:
                apps = [f for f in os.listdir(applications_path) if f.endswith('.app')]
                zscaler_apps = [app for app in apps if 'zscaler' in app.lower() or 'ztb' in app.lower()]

                if zscaler_apps:
                    self.logger.info(f"Found Zscaler-related applications: {zscaler_apps}")
                    return True, os.path.join(applications_path, zscaler_apps[0])
                else:
                    self.logger.warning("No Zscaler applications found in /Applications")
                    return False, None

            except Exception as e:
                self.logger.error(f"Error listing applications: {e}")
                return False, None

        except Exception as e:
            self.logger.error(f"Error verifying installation: {e}")
            return False, None
    
    def close_installer(self):
        """
        Close the installer window
        """
        try:
            self.logger.info("Closing installer...")

            # Try to click Close button
            close_texts = ["Close", "Done", "Finish", "Quit"]

            for text in close_texts:
                if self.click_button_by_text(text):
                    return True

            # Try pressing Cmd+Q to quit
            if PYAUTOGUI_AVAILABLE:
                pyautogui.hotkey('cmd', 'q')
                time.sleep(2)

            return True

        except Exception as e:
            self.logger.error(f"Error closing installer: {e}")
            return False

    def mount_dmg_manual(self, dmg_path):
        """
        Manually mount a DMG file using hdiutil with automatic license agreement
        """
        try:
            self.logger.info(f"Mounting DMG: {dmg_path}")

            if not os.path.exists(dmg_path):
                return (False, f"DMG file not found: {dmg_path}", None)

            # First try to mount with automatic agreement to license
            mount_command = ["hdiutil", "attach", dmg_path, "-nobrowse", "-noautoopen"]

            # Use Popen to handle interactive license agreement
            process = subprocess.Popen(
                mount_command,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # Send "Y" to agree to license if prompted
            try:
                stdout, stderr = process.communicate(input="Y\n", timeout=60)

                if process.returncode == 0:
                    # Parse the output to find the mount point
                    lines = stdout.strip().split('\n')
                    mount_point = None

                    for line in lines:
                        if '/Volumes/' in line:
                            # Look for the mount point in the line
                            # hdiutil output format: /dev/diskXsY ... /Volumes/VolumeName
                            parts = line.split('\t')  # hdiutil uses tabs
                            for part in parts:
                                part = part.strip()
                                if part.startswith('/Volumes/'):
                                    mount_point = part
                                    break
                            if not mount_point:
                                # Fallback: split by spaces and look for /Volumes/
                                parts = line.split()
                                for part in parts:
                                    if part.startswith('/Volumes/'):
                                        mount_point = part
                                        break
                            if mount_point:
                                break

                    # If we still don't have a mount point, try to find it by listing /Volumes/
                    if not mount_point:
                        try:
                            volumes = os.listdir('/Volumes/')
                            for vol in volumes:
                                if 'Zscaler' in vol or 'ZTB' in vol:
                                    mount_point = f'/Volumes/{vol}'
                                    break
                        except:
                            pass

                    if mount_point:
                        self.logger.info(f"DMG mounted successfully at: {mount_point}")
                        return (True, "DMG mounted successfully", mount_point)
                    else:
                        return (False, "Could not determine mount point", None)
                else:
                    self.logger.error(f"Mount failed with return code {process.returncode}")
                    self.logger.error(f"stderr: {stderr}")
                    return (False, f"Mount failed: {stderr}", None)

            except subprocess.TimeoutExpired:
                process.kill()
                return (False, "Mount operation timed out", None)

        except Exception as e:
            self.logger.error(f"Error mounting DMG: {e}")
            return (False, f"Error mounting DMG: {e}", None)

    def unmount_dmg_manual(self, mount_point):
        """
        Manually unmount a DMG using hdiutil
        """
        try:
            self.logger.info(f"Unmounting DMG: {mount_point}")

            unmount_command = ["hdiutil", "detach", mount_point, "-force"]

            result = subprocess.run(
                unmount_command,
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0:
                self.logger.info(f"DMG unmounted successfully")
                return (True, "DMG unmounted successfully", None)
            else:
                return (False, f"Unmount failed: {result.stderr}", None)

        except Exception as e:
            self.logger.error(f"Error unmounting DMG: {e}")
            return (False, f"Error unmounting DMG: {e}", None)


def test_ui_automation_installation():
    """
    Complete UI automation test for DMG installation following the exact flow:
    1. Mount DMG
    2. Handle DMG license agreement (click Agree)
    3. Click on .pkg file in DMG
    4. Handle installer screens (Introduction → License → Destination → Installation Type → Installation)
    5. Select Edge browser and uncheck Chrome
    6. Wait for installation completion
    7. Verify installation in Applications folder
    8. Cleanup
    """
    print("=" * 80)
    print("🖥️  UI AUTOMATION INSTALLATION TEST")
    print("=" * 80)

    # Initialize logger
    logger = Logger.initialize_logger("ui_automation_test.log", log_level="DEBUG")

    # Check available automation tools
    automation_methods = []
    if APPIUM_AVAILABLE:
        automation_methods.append("Appium")
    if PYAUTOGUI_AVAILABLE:
        automation_methods.append("PyAutoGUI")

    if not automation_methods:
        print("❌ No UI automation tools available.")
        print("   Please install: pip install Appium-Python-Client pyautogui")
        return False

    print(f"🔧 Available automation methods: {', '.join(automation_methods)}")

    try:
        # Determine the correct resource path
        current_dir = os.getcwd()
        if "test_suite" in current_dir:
            # We're running from test directory, go up to project root
            project_root = Path(__file__).parent.parent.parent.parent
            resource_path = os.path.join(project_root, "OS_Mac", "resource")
        else:
            # We're running from project root
            resource_path = os.path.join(current_dir, "OS_Mac", "resource")

        print(f"📂 Resource directory: {resource_path}")

        # Verify resource directory exists
        if not os.path.exists(resource_path):
            print(f"❌ Resource directory not found: {resource_path}")
            return False

        # Create ZTB installer instance (skip path verification since we handle it manually)
        try:
            installer = ZTBInstaller(verify_dmg_exists=False, log_handle=logger)
        except AssertionError:
            # Handle path assertion error by creating a mock installer
            print("⚠️  Creating installer with manual path configuration...")
            installer = type('MockInstaller', (), {})()
            installer.logger = logger
            installer.mounted_volumes = []

        ui_automation = MacOSInstallerUIAutomation(logger)

        # Try to setup Appium driver
        appium_ready = ui_automation.setup_appium_driver()
        if appium_ready:
            print("✅ Appium driver initialized - using advanced UI automation")
        else:
            print("⚠️  Appium not available - using fallback automation methods")

        # Use the DMG file
        component_dmg = "ZscalerZTBSetup-arm64-0.0.1.dmg"

        print(f"📁 Testing UI automation installation of: {component_dmg}")

        # Step 1: Mount the DMG
        dmg_path = os.path.join(resource_path, component_dmg)
        print(f"\n🔄 Step 1: Mounting DMG...")

        mount_result = ui_automation.mount_dmg_manual(dmg_path)
        if not mount_result[0]:
            print(f"❌ Failed to mount DMG: {mount_result[1]}")
            return False

        mount_path = mount_result[2]
        print(f"✅ DMG mounted successfully at: {mount_path}")

        try:
            # Step 2: Handle DMG license agreement (first screen)
            print(f"\n📋 Step 2: Handling DMG license agreement...")
            time.sleep(3)  # Wait for DMG window to appear

            # Check if license dialog is visible and try to handle it
            print("   Looking for license agreement dialog...")

            # Try to find and click Agree button using AppleScript
            agree_clicked = ui_automation.click_button_by_applescript("Agree")

            if not agree_clicked:
                print("   ⚠️  No license dialog found or already handled during mounting")
                print("   This is normal if the license was accepted during DMG mounting")

            # Step 3: Click on .pkg file in DMG
            print(f"\n🎯 Step 3: Clicking on .pkg file in DMG...")
            if not ui_automation.click_pkg_file_in_dmg(mount_path):
                print("❌ Failed to click .pkg file")
                return False

            print("✅ .pkg file clicked, installer should be launching...")

            # Step 4: Find and activate installer window
            print(f"\n🔍 Step 4: Looking for installer window...")
            time.sleep(3)  # Wait for installer to fully load

            installer_window = ui_automation.find_installer_window("Install")
            if not installer_window:
                installer_window = ui_automation.find_installer_window("Installer")

            if installer_window:
                ui_automation.activate_window(installer_window)
                print(f"✅ Found and activated installer window: {installer_window}")

                print(f"\n🤖 Step 5: Starting installer UI automation...")

                # Step 5a: Handle introduction screen
                print("   5a: Handling introduction screen...")
                time.sleep(2)
                ui_automation.click_continue_button()

                # Step 5b: Handle installer license agreement
                print("   5b: Handling installer license agreement...")
                time.sleep(2)
                ui_automation.handle_license_agreement()

                # Step 5c: Handle destination selection
                print("   5c: Handling destination selection...")
                time.sleep(2)
                ui_automation.click_continue_button()

                # Step 5d: Handle installation type and browser selection
                print("   5d: Handling installation type and browser selection...")
                time.sleep(2)
                ui_automation.handle_installation_type()  # This includes browser selection

                # Step 5e: Handle install button
                print("   5e: Clicking Install button...")
                time.sleep(2)
                install_clicked = ui_automation.click_element_by_text("Install") or \
                                ui_automation.click_button_by_applescript("Install")

                if install_clicked:
                    print("✅ Install button clicked")
                else:
                    print("⚠️  Could not click Install button automatically")

                # Step 6: Wait for installation to complete
                print(f"\n⏳ Step 6: Waiting for installation to complete...")
                if ui_automation.wait_for_installation_complete(timeout=300):
                    print("✅ Installation completed successfully")

                    # Step 6a: Close installer
                    print("   6a: Closing installer...")
                    ui_automation.close_installer()

                    # Step 7: Verify installation
                    print(f"\n🔍 Step 7: Verifying installation...")
                    verification_result = ui_automation.verify_installation()

                    if verification_result[0]:
                        print(f"✅ Installation verified: {verification_result[1]}")
                    else:
                        print("⚠️  Could not verify installation automatically")
                        print("   Please check /Applications folder manually")

                else:
                    print("⏰ Installation timed out or failed")
                    return False

            else:
                print("❌ Could not find installer window")
                return False

        finally:
            # Step 8: Cleanup
            print(f"\n🧹 Step 8: Cleaning up...")
            time.sleep(2)  # Wait a bit before unmounting

            # Cleanup Appium driver
            ui_automation.teardown_appium_driver()

            unmount_result = ui_automation.unmount_dmg_manual(mount_path)
            if unmount_result[0]:
                print(f"✅ DMG unmounted successfully")
            else:
                print(f"❌ Failed to unmount DMG: {unmount_result[1]}")

        # Final cleanup - try to unmount any remaining volumes
        try:
            subprocess.run(["hdiutil", "detach", mount_path, "-force"],
                         capture_output=True, timeout=10)
            print("✅ Final cleanup completed successfully")
        except:
            print("⚠️  Final cleanup completed with warnings")

        print(f"\n🎉 UI automation installation test completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Error during UI automation test: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    test_ui_automation_installation()

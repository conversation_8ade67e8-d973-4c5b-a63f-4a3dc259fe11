2025-07-31 13:51:09,527 - ui_automation_test.log - INFO - initialize_logger:60 - Logger initialized: ui_automation_test.log
2025-07-31 13:51:09,527 - ui_automation_test.log - INFO - initialize_logger:61 - Log file: /Users/<USER>/Z-Repos/ztb_automation/OS_Mac/test_suite/agent_installation/Reports/logs/ui_automation_test.log_20250731_135109.log
2025-07-31 13:51:09,527 - ui_automation_test.log - WARNING - setup_appium_driver:81 - Failed to setup Appium driver: missing 1 required keyword-only argument: 'options' (instance of driver `options.Options` class)
2025-07-31 13:51:09,527 - ui_automation_test.log - INFO - setup_appium_driver:82 - Will use fallback automation methods
2025-07-31 13:51:09,527 - ui_automation_test.log - INFO - mount_dmg_manual:561 - Mounting DMG: /Users/<USER>/Z-Repos/ztb_automation/OS_Mac/resource/ZscalerZTBSetup-arm64-0.0.1.dmg
2025-07-31 13:51:10,023 - ui_automation_test.log - INFO - mount_dmg_manual:619 - DMG mounted successfully at: /Volumes/Zscaler Zero Trust Browser Installer

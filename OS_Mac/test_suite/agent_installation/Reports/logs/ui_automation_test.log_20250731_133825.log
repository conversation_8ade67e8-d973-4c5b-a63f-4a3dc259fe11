2025-07-31 13:38:25,204 - ui_automation_test.log - INFO - initialize_logger:60 - Logger initialized: ui_automation_test.log
2025-07-31 13:38:25,204 - ui_automation_test.log - INFO - initialize_logger:61 - Log file: /Users/<USER>/Z-Repos/ztb_automation/OS_Mac/test_suite/agent_installation/Reports/logs/ui_automation_test.log_20250731_133825.log
2025-07-31 13:38:25,204 - ui_automation_test.log - INFO - mount_dmg_manual:507 - Mounting DMG: /Users/<USER>/Z-Repos/ztb_automation/OS_Mac/resource/ZscalerZTBSetup-arm64-0.0.1.dmg
2025-07-31 13:38:25,732 - ui_automation_test.log - INFO - mount_dmg_manual:565 - DMG mounted successfully at: /Volumes/Zscaler Zero Trust Browser Installer
2025-07-31 13:38:28,738 - ui_automation_test.log - INFO - handle_license_agreement:261 - Handling license agreement...
2025-07-31 13:38:28,738 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Agree
2025-07-31 13:38:28,959 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Agree' not found or not clickable
2025-07-31 13:38:28,960 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: I Agree
2025-07-31 13:38:29,152 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'I Agree' not found or not clickable
2025-07-31 13:38:29,152 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Accept
2025-07-31 13:38:29,342 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Accept' not found or not clickable
2025-07-31 13:38:29,342 - ui_automation_test.log - INFO - click_button_by_text:190 - Looking for button with text: Agree
2025-07-31 13:38:39,674 - ui_automation_test.log - WARNING - click_button_by_text:213 - Button 'Agree' not found within 10 seconds
2025-07-31 13:38:39,674 - ui_automation_test.log - INFO - click_button_by_text:190 - Looking for button with text: I Agree
2025-07-31 13:38:50,027 - ui_automation_test.log - WARNING - click_button_by_text:213 - Button 'I Agree' not found within 10 seconds
2025-07-31 13:38:50,028 - ui_automation_test.log - INFO - click_button_by_text:190 - Looking for button with text: Accept
2025-07-31 13:39:00,305 - ui_automation_test.log - WARNING - click_button_by_text:213 - Button 'Accept' not found within 10 seconds
2025-07-31 13:39:03,411 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Continue
2025-07-31 13:39:03,623 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Continue' not found or not clickable
2025-07-31 13:39:03,623 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Next
2025-07-31 13:39:03,816 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Next' not found or not clickable
2025-07-31 13:39:03,816 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Install
2025-07-31 13:39:04,075 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Install' not found or not clickable
2025-07-31 13:39:04,076 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Agree
2025-07-31 13:39:04,298 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Agree' not found or not clickable
2025-07-31 13:39:04,298 - ui_automation_test.log - INFO - click_button_by_text:190 - Looking for button with text: Continue
2025-07-31 13:39:14,553 - ui_automation_test.log - WARNING - click_button_by_text:213 - Button 'Continue' not found within 10 seconds
2025-07-31 13:39:14,554 - ui_automation_test.log - INFO - click_button_by_text:190 - Looking for button with text: Next
2025-07-31 13:39:24,860 - ui_automation_test.log - WARNING - click_button_by_text:213 - Button 'Next' not found within 10 seconds
2025-07-31 13:39:24,860 - ui_automation_test.log - INFO - click_button_by_text:190 - Looking for button with text: Install
2025-07-31 13:39:35,145 - ui_automation_test.log - WARNING - click_button_by_text:213 - Button 'Install' not found within 10 seconds
2025-07-31 13:39:35,146 - ui_automation_test.log - INFO - click_button_by_text:190 - Looking for button with text: Agree
2025-07-31 13:39:45,387 - ui_automation_test.log - WARNING - click_button_by_text:213 - Button 'Agree' not found within 10 seconds
2025-07-31 13:39:49,418 - ui_automation_test.log - INFO - click_continue_button:249 - Clicked at location (927, 681)
2025-07-31 13:39:53,440 - ui_automation_test.log - INFO - click_continue_button:249 - Clicked at location (800, 500)
2025-07-31 13:39:57,460 - ui_automation_test.log - INFO - click_continue_button:249 - Clicked at location (700, 450)
2025-07-31 13:40:02,464 - ui_automation_test.log - INFO - click_pkg_file_in_dmg:294 - Looking for .pkg file in DMG to click...
2025-07-31 13:40:02,465 - ui_automation_test.log - ERROR - click_pkg_file_in_dmg:315 - Error clicking .pkg file: [Errno 2] No such file or directory: '/Volumes/Zscaler Zero Trust Browser Installer'
2025-07-31 13:40:04,466 - ui_automation_test.log - INFO - unmount_dmg_manual:587 - Unmounting DMG: /Volumes/Zscaler Zero Trust Browser Installer

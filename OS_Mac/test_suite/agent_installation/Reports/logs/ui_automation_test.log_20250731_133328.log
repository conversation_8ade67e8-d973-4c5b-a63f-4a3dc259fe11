2025-07-31 13:33:28,496 - ui_automation_test.log - INFO - initialize_logger:60 - Logger initialized: ui_automation_test.log
2025-07-31 13:33:28,496 - ui_automation_test.log - INFO - initialize_logger:61 - Log file: /Users/<USER>/Z-Repos/ztb_automation/OS_Mac/test_suite/agent_installation/Reports/logs/ui_automation_test.log_20250731_133328.log
2025-07-31 13:33:28,496 - ui_automation_test.log - INFO - mount_dmg_manual:504 - Mounting DMG: /Users/<USER>/Z-Repos/ztb_automation/OS_Mac/resource/ZscalerZTBSetup-arm64-0.0.1.dmg
2025-07-31 13:33:29,340 - ui_automation_test.log - INFO - mount_dmg_manual:541 - DMG mounted successfully at: /Volumes/Zscaler
2025-07-31 13:33:32,344 - ui_automation_test.log - INFO - handle_license_agreement:258 - Handling license agreement...
2025-07-31 13:33:32,344 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Agree
2025-07-31 13:33:37,473 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Agree' not found or not clickable
2025-07-31 13:33:37,473 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: I Agree
2025-07-31 13:33:37,638 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'I Agree' not found or not clickable
2025-07-31 13:33:37,639 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Accept
2025-07-31 13:33:37,802 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Accept' not found or not clickable
2025-07-31 13:33:37,803 - ui_automation_test.log - INFO - click_button_by_text:190 - Looking for button with text: Agree
2025-07-31 13:33:38,264 - ui_automation_test.log - ERROR - click_button_by_text:214 - Error clicking button 'Agree': The confidence keyword argument is only available if OpenCV is installed.
2025-07-31 13:33:38,265 - ui_automation_test.log - INFO - click_button_by_text:190 - Looking for button with text: I Agree
2025-07-31 13:33:38,525 - ui_automation_test.log - ERROR - click_button_by_text:214 - Error clicking button 'I Agree': The confidence keyword argument is only available if OpenCV is installed.
2025-07-31 13:33:38,525 - ui_automation_test.log - INFO - click_button_by_text:190 - Looking for button with text: Accept
2025-07-31 13:33:38,783 - ui_automation_test.log - ERROR - click_button_by_text:214 - Error clicking button 'Accept': The confidence keyword argument is only available if OpenCV is installed.
2025-07-31 13:33:41,852 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Continue
2025-07-31 13:33:42,082 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Continue' not found or not clickable
2025-07-31 13:33:42,082 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Next
2025-07-31 13:33:42,270 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Next' not found or not clickable
2025-07-31 13:33:42,270 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Install
2025-07-31 13:33:42,463 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Install' not found or not clickable
2025-07-31 13:33:42,464 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Agree
2025-07-31 13:33:42,652 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Agree' not found or not clickable
2025-07-31 13:33:42,652 - ui_automation_test.log - INFO - click_button_by_text:190 - Looking for button with text: Continue
2025-07-31 13:33:42,927 - ui_automation_test.log - ERROR - click_button_by_text:214 - Error clicking button 'Continue': The confidence keyword argument is only available if OpenCV is installed.
2025-07-31 13:33:42,927 - ui_automation_test.log - INFO - click_button_by_text:190 - Looking for button with text: Next
2025-07-31 13:33:43,180 - ui_automation_test.log - ERROR - click_button_by_text:214 - Error clicking button 'Next': The confidence keyword argument is only available if OpenCV is installed.
2025-07-31 13:33:43,181 - ui_automation_test.log - INFO - click_button_by_text:190 - Looking for button with text: Install
2025-07-31 13:33:43,418 - ui_automation_test.log - ERROR - click_button_by_text:214 - Error clicking button 'Install': The confidence keyword argument is only available if OpenCV is installed.
2025-07-31 13:33:43,418 - ui_automation_test.log - INFO - click_button_by_text:190 - Looking for button with text: Agree
2025-07-31 13:33:43,657 - ui_automation_test.log - ERROR - click_button_by_text:214 - Error clicking button 'Agree': The confidence keyword argument is only available if OpenCV is installed.
2025-07-31 13:33:47,678 - ui_automation_test.log - INFO - click_continue_button:246 - Clicked at location (927, 681)
2025-07-31 13:33:51,702 - ui_automation_test.log - INFO - click_continue_button:246 - Clicked at location (800, 500)
2025-07-31 13:33:55,726 - ui_automation_test.log - INFO - click_continue_button:246 - Clicked at location (700, 450)
2025-07-31 13:34:00,731 - ui_automation_test.log - INFO - click_pkg_file_in_dmg:291 - Looking for .pkg file in DMG to click...
2025-07-31 13:34:00,732 - ui_automation_test.log - ERROR - click_pkg_file_in_dmg:312 - Error clicking .pkg file: [Errno 2] No such file or directory: '/Volumes/Zscaler'
2025-07-31 13:34:02,735 - ui_automation_test.log - INFO - unmount_dmg_manual:563 - Unmounting DMG: /Volumes/Zscaler

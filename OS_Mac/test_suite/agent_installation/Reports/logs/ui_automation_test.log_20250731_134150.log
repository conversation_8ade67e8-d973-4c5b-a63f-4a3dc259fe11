2025-07-31 13:41:50,676 - ui_automation_test.log - INFO - initialize_logger:60 - Logger initialized: ui_automation_test.log
2025-07-31 13:41:50,676 - ui_automation_test.log - INFO - initialize_logger:61 - Log file: /Users/<USER>/Z-Repos/ztb_automation/OS_Mac/test_suite/agent_installation/Reports/logs/ui_automation_test.log_20250731_134150.log
2025-07-31 13:41:50,676 - ui_automation_test.log - INFO - mount_dmg_manual:468 - Mounting DMG: /Users/<USER>/Z-Repos/ztb_automation/OS_Mac/resource/ZscalerZTBSetup-arm64-0.0.1.dmg
2025-07-31 13:41:50,804 - ui_automation_test.log - INFO - mount_dmg_manual:526 - DMG mounted successfully at: /Volumes/Zscaler Zero Trust Browser Installer
2025-07-31 13:41:53,806 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Agree
2025-07-31 13:41:54,012 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Agree' not found or not clickable
2025-07-31 13:41:54,012 - ui_automation_test.log - INFO - click_pkg_file_in_dmg:264 - Looking for .pkg file in DMG to click...
2025-07-31 13:41:54,013 - ui_automation_test.log - INFO - click_pkg_file_in_dmg:276 - Found .pkg file: ZscalerZTBSetup-arm64-0.0.1.pkg
2025-07-31 13:42:00,337 - ui_automation_test.log - INFO - find_installer_window:83 - Found installer process: Installer
2025-07-31 13:42:01,519 - ui_automation_test.log - INFO - activate_window:116 - Activated process: Installer
2025-07-31 13:42:03,525 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Continue
2025-07-31 13:42:03,740 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Continue' not found or not clickable
2025-07-31 13:42:03,741 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Next
2025-07-31 13:42:03,925 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Next' not found or not clickable
2025-07-31 13:42:03,925 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Install
2025-07-31 13:42:04,104 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Install' not found or not clickable
2025-07-31 13:42:04,104 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Agree
2025-07-31 13:42:04,301 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Agree' not found or not clickable
2025-07-31 13:42:04,301 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: Continue (timeout: 10s)
2025-07-31 13:42:04,301 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:04,301 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: Next (timeout: 10s)
2025-07-31 13:42:04,301 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:04,301 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: Install (timeout: 10s)
2025-07-31 13:42:04,301 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:04,301 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: Agree (timeout: 10s)
2025-07-31 13:42:04,301 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:08,365 - ui_automation_test.log - INFO - click_continue_button:219 - Clicked at location (927, 681)
2025-07-31 13:42:12,385 - ui_automation_test.log - INFO - click_continue_button:219 - Clicked at location (800, 500)
2025-07-31 13:42:16,405 - ui_automation_test.log - INFO - click_continue_button:219 - Clicked at location (700, 450)
2025-07-31 13:42:18,409 - ui_automation_test.log - INFO - handle_license_agreement:231 - Handling license agreement...
2025-07-31 13:42:18,410 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Agree
2025-07-31 13:42:18,613 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Agree' not found or not clickable
2025-07-31 13:42:18,613 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: I Agree
2025-07-31 13:42:18,797 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'I Agree' not found or not clickable
2025-07-31 13:42:18,797 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Accept
2025-07-31 13:42:18,977 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Accept' not found or not clickable
2025-07-31 13:42:18,977 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: Agree (timeout: 10s)
2025-07-31 13:42:18,977 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:18,977 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: I Agree (timeout: 10s)
2025-07-31 13:42:18,977 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:18,977 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: Accept (timeout: 10s)
2025-07-31 13:42:18,977 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:22,022 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Continue
2025-07-31 13:42:22,215 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Continue' not found or not clickable
2025-07-31 13:42:22,215 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Next
2025-07-31 13:42:22,412 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Next' not found or not clickable
2025-07-31 13:42:22,412 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Install
2025-07-31 13:42:22,589 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Install' not found or not clickable
2025-07-31 13:42:22,589 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Agree
2025-07-31 13:42:22,773 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Agree' not found or not clickable
2025-07-31 13:42:22,773 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: Continue (timeout: 10s)
2025-07-31 13:42:22,773 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:22,773 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: Next (timeout: 10s)
2025-07-31 13:42:22,773 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:22,773 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: Install (timeout: 10s)
2025-07-31 13:42:22,773 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:22,773 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: Agree (timeout: 10s)
2025-07-31 13:42:22,773 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:26,791 - ui_automation_test.log - INFO - click_continue_button:219 - Clicked at location (927, 681)
2025-07-31 13:42:30,813 - ui_automation_test.log - INFO - click_continue_button:219 - Clicked at location (800, 500)
2025-07-31 13:42:34,834 - ui_automation_test.log - INFO - click_continue_button:219 - Clicked at location (700, 450)
2025-07-31 13:42:36,840 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Continue
2025-07-31 13:42:37,034 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Continue' not found or not clickable
2025-07-31 13:42:37,035 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Next
2025-07-31 13:42:37,218 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Next' not found or not clickable
2025-07-31 13:42:37,218 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Install
2025-07-31 13:42:37,400 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Install' not found or not clickable
2025-07-31 13:42:37,401 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Agree
2025-07-31 13:42:37,576 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Agree' not found or not clickable
2025-07-31 13:42:37,576 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: Continue (timeout: 10s)
2025-07-31 13:42:37,576 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:37,576 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: Next (timeout: 10s)
2025-07-31 13:42:37,576 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:37,577 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: Install (timeout: 10s)
2025-07-31 13:42:37,577 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:37,577 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: Agree (timeout: 10s)
2025-07-31 13:42:37,577 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:41,598 - ui_automation_test.log - INFO - click_continue_button:219 - Clicked at location (927, 681)
2025-07-31 13:42:45,622 - ui_automation_test.log - INFO - click_continue_button:219 - Clicked at location (800, 500)
2025-07-31 13:42:49,643 - ui_automation_test.log - INFO - click_continue_button:219 - Clicked at location (700, 450)
2025-07-31 13:42:51,647 - ui_automation_test.log - INFO - handle_installation_type:331 - Handling installation type selection...
2025-07-31 13:42:51,648 - ui_automation_test.log - INFO - handle_browser_selection:293 - Handling browser selection...
2025-07-31 13:42:51,648 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Edge
2025-07-31 13:42:51,847 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Edge' not found or not clickable
2025-07-31 13:42:51,847 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: Edge (timeout: 10s)
2025-07-31 13:42:51,847 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:51,847 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Microsoft Edge
2025-07-31 13:42:52,032 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Microsoft Edge' not found or not clickable
2025-07-31 13:42:52,032 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: Microsoft Edge (timeout: 10s)
2025-07-31 13:42:52,032 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:52,032 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: edge
2025-07-31 13:42:52,220 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'edge' not found or not clickable
2025-07-31 13:42:52,220 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: edge (timeout: 10s)
2025-07-31 13:42:52,220 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:52,220 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Chrome
2025-07-31 13:42:52,415 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Chrome' not found or not clickable
2025-07-31 13:42:52,415 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: Chrome (timeout: 10s)
2025-07-31 13:42:52,415 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:52,415 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Google Chrome
2025-07-31 13:42:52,611 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Google Chrome' not found or not clickable
2025-07-31 13:42:52,612 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: Google Chrome (timeout: 10s)
2025-07-31 13:42:52,612 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:52,612 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: chrome
2025-07-31 13:42:52,810 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'chrome' not found or not clickable
2025-07-31 13:42:52,811 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: chrome (timeout: 10s)
2025-07-31 13:42:52,811 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:54,816 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Continue
2025-07-31 13:42:55,042 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Continue' not found or not clickable
2025-07-31 13:42:55,043 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Next
2025-07-31 13:42:55,235 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Next' not found or not clickable
2025-07-31 13:42:55,235 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Install
2025-07-31 13:42:55,412 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Install' not found or not clickable
2025-07-31 13:42:55,412 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Agree
2025-07-31 13:42:55,604 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Agree' not found or not clickable
2025-07-31 13:42:55,604 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: Continue (timeout: 10s)
2025-07-31 13:42:55,604 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:55,604 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: Next (timeout: 10s)
2025-07-31 13:42:55,604 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:55,604 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: Install (timeout: 10s)
2025-07-31 13:42:55,604 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:55,604 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: Agree (timeout: 10s)
2025-07-31 13:42:55,604 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:42:59,623 - ui_automation_test.log - INFO - click_continue_button:219 - Clicked at location (927, 681)
2025-07-31 13:43:03,645 - ui_automation_test.log - INFO - click_continue_button:219 - Clicked at location (800, 500)
2025-07-31 13:43:07,664 - ui_automation_test.log - INFO - click_continue_button:219 - Clicked at location (700, 450)
2025-07-31 13:43:09,671 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Install
2025-07-31 13:43:09,876 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Install' not found or not clickable
2025-07-31 13:43:09,876 - ui_automation_test.log - INFO - click_button_by_text:186 - PyAutoGUI text detection not available for: Install (timeout: 10s)
2025-07-31 13:43:09,876 - ui_automation_test.log - INFO - click_button_by_text:187 - Use AppleScript method or coordinate-based clicking instead
2025-07-31 13:43:09,876 - ui_automation_test.log - INFO - wait_for_installation_complete:373 - Waiting for installation to complete (timeout: 300s)...
2025-07-31 13:43:09,876 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:43:10,061 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:43:10,062 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:43:10,243 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:43:10,243 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:43:10,433 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:43:10,434 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:43:10,618 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:43:15,622 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:43:15,816 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:43:15,816 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:43:15,995 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:43:15,995 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:43:16,164 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:43:16,164 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:43:16,332 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:43:21,333 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:43:21,538 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:43:21,538 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:43:21,727 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:43:21,728 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:43:21,910 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:43:21,910 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:43:22,092 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:43:27,098 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:43:27,305 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:43:27,306 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:43:27,513 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:43:27,514 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:43:27,691 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:43:27,692 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:43:27,871 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:43:32,876 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:43:33,078 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:43:33,078 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:43:33,256 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:43:33,256 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:43:33,433 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:43:33,433 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:43:33,621 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:43:38,627 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:43:38,809 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:43:38,809 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:43:38,993 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:43:38,993 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:43:39,176 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:43:39,176 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:43:39,345 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:43:44,351 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:43:44,562 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:43:44,562 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:43:44,751 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:43:44,751 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:43:44,960 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:43:44,960 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:43:45,135 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:43:50,141 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:43:50,351 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:43:50,351 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:43:50,521 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:43:50,521 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:43:50,680 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:43:50,681 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:43:50,841 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:43:55,846 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:43:56,062 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:43:56,062 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:43:56,258 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:43:56,258 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:43:56,448 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:43:56,449 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:43:56,625 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:44:01,628 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:44:01,788 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:44:01,788 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:44:01,951 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:44:01,951 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:44:02,127 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:44:02,127 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:44:02,288 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:44:07,294 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:44:07,497 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:44:07,497 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:44:07,683 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:44:07,683 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:44:07,874 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:44:07,874 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:44:08,061 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:44:13,067 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:44:13,294 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:44:13,294 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:44:13,483 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:44:13,483 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:44:13,655 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:44:13,656 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:44:13,827 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:44:18,833 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:44:19,042 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:44:19,042 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:44:19,284 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:44:19,284 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:44:19,457 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:44:19,457 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:44:19,645 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:44:24,650 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:44:24,847 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:44:24,847 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:44:25,031 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:44:25,031 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:44:25,216 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:44:25,216 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:44:25,403 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:44:30,408 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:44:30,610 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:44:30,610 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:44:30,798 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:44:30,799 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:44:30,984 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:44:30,984 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:44:31,172 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:44:36,177 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:44:36,352 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:44:36,352 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:44:36,532 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:44:36,532 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:44:36,689 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:44:36,689 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:44:36,859 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:44:41,865 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:44:42,044 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:44:42,044 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:44:42,220 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:44:42,220 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:44:42,398 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:44:42,398 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:44:42,571 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:44:47,576 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:44:47,784 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:44:47,784 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:44:47,974 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:44:47,974 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:44:48,164 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:44:48,164 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:44:48,356 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:44:53,362 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:44:53,566 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:44:53,566 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:44:53,731 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:44:53,731 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:44:53,891 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:44:53,891 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:44:54,062 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:44:59,067 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:44:59,266 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:44:59,266 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:44:59,448 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:44:59,448 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:44:59,613 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:44:59,614 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:44:59,775 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:45:04,775 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:45:04,973 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:45:04,973 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:45:05,171 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:45:05,172 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:45:05,362 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:45:05,362 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:45:05,540 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:45:10,545 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:45:10,707 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:45:10,707 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:45:10,877 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:45:10,878 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:45:11,064 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:45:11,064 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:45:11,240 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:45:16,240 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:45:16,432 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:45:16,433 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:45:16,617 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:45:16,617 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:45:16,792 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:45:16,792 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:45:16,990 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:45:21,995 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:45:22,190 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:45:22,190 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:45:22,374 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:45:22,375 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:45:22,562 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:45:22,562 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:45:22,748 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:45:27,753 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:45:27,954 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:45:27,954 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:45:28,140 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:45:28,141 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:45:28,313 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:45:28,313 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:45:28,492 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:45:33,495 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Close
2025-07-31 13:45:33,689 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Close' not found or not clickable
2025-07-31 13:45:33,689 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Done
2025-07-31 13:45:33,870 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Done' not found or not clickable
2025-07-31 13:45:33,870 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Finish
2025-07-31 13:45:34,064 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Finish' not found or not clickable
2025-07-31 13:45:34,064 - ui_automation_test.log - INFO - click_button_by_applescript:131 - Looking for button with text: Summary
2025-07-31 13:45:34,240 - ui_automation_test.log - WARNING - click_button_by_applescript:174 - Button 'Summary' not found or not clickable
2025-07-31 13:45:38,322 - ui_automation_test.log - INFO - unmount_dmg_manual:548 - Unmounting DMG: /Volumes/Zscaler Zero Trust Browser Installer
2025-07-31 13:45:38,451 - ui_automation_test.log - INFO - unmount_dmg_manual:560 - DMG unmounted successfully

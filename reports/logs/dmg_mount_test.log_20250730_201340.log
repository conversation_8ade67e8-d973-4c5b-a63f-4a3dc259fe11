2025-07-30 20:13:40,626 - dmg_mount_test.log - INFO - initialize_logger:60 - Logger initialized: dmg_mount_test.log
2025-07-30 20:13:40,626 - dmg_mount_test.log - INFO - initialize_logger:61 - Log file: /Users/<USER>/Z-Repos/ztb_automation/Reports/logs/dmg_mount_test.log_20250730_201340.log
2025-07-30 20:13:40,626 - dmg_mount_test.log - INFO - __init__:51 - ZTB Installer initialized
2025-07-30 20:13:40,626 - dmg_mount_test.log - INFO - list_available_dmg_installers:67 - Found 1 DMG files: ['ZscalerZTBSetup-arm64-0.0.1.dmg']
2025-07-30 20:13:40,626 - dmg_mount_test.log - INFO - mount_dmg:107 - Mounting DMG file: /Users/<USER>/Z-Repos/ztb_automation/OS_Mac/resource/ZscalerZTBSetup-arm64-0.0.1.dmg
2025-07-30 20:13:41,224 - dmg_mount_test.log - DEBUG - mount_dmg:131 - hdiutil stdout: End User Subscription Agreement

Last Updated: March 6, 2025

Introduction

This End User Subscription Agreement (the “Agreement”) governs the
purchase, access, and use of Products by the Customer listed on an
Order (hereinafter “Customer” or “You” or “Your”). In order to use
or receive the benefits of any Product, You must purchase the
applicable Product through an Order. This Agreement applies to all
current and future use and access of Zscaler products and services
by Customer unless expressly agreed otherwise by Customer and
Zscaler.

IF YOU HAVE ARRIVED AT THIS PAGE DURING THE PROCESS OF INSTALLING,
DOWNLOADING, ACCESSING, OR DEPLOYING A PRODUCT, YOU ACKNOWLEDGE AND
AGREE THAT BY PROCEEDING WITH THE INSTALLATION, DOWNLOAD, ACCESS,
DEPLOYMENT, OR USE OF THE PRODUCT, YOU AGREE TO BE BOUND BY THE
TERMS AND CONDITIONS IN THIS AGREEMENT. IF YOU DO NOT UNCONDITIONALLY
AGREE TO THE FOREGOING, DISCONTINUE THE INSTALLATION, DOWNLOAD,
ACCESS, DEPLOYMENT, OR USE. IF YOU PROCEED WITH INSTALLATION,
DOWNLOAD, ACCESS, DEPLOYMENT, OR USE, YOU ARE REPRESENTING AND
WARRANTING THAT YOU ARE AUTHORIZED TO BIND THE CUSTOMER.

This Agreement may be periodically updated and the current version
will be posted at www.zscaler.com/legal/overview. Your continued
use of the Products after a revised Agreement has been posted
constitutes your acceptance of its terms.

1. Definitions

“Affiliate” means any entity controlled, directly or indirectly,
by, under common control with, or controlling, a party, and
specifically includes without limitation, subsidiaries, partnerships,
joint ventures, and other entities or operations for which the party
has operational or management control. For the purposes of this
definition, “control” means the power to direct, or cause the
direction of, the management and policies of such entity whether
by contract, law, or ownership of the majority of the voting shares
or assets of another entity.

“Authorized User” means an employee, agent, contractor, or other
third party authorized by Customer and/or its Affiliates to access,
use, download, deploy, or install the Products.

“Customer Data” means all data or information submitted by Customer
to the Products.

“Deployment Services” means the deployment and related services for
the Products provided by Zscaler to Customer as described in the
Order.

“Documentation” means the documentation and usage guidelines for
the Products, as updated from time to time by Zscaler.

“Fees” means any fees paid or to be paid for Products under an
Order.

“Force Majeure Event” means any circumstances which are unforeseeable,
and beyond the reasonable control of the party affected, including
but not limited to acts of God, acts of government, flood, fire,
earthquakes, civil unrest, acts of terror, strikes or other labor
problems, Internet service provider or hosting facility failures
or delays, hardware, software or power systems not provided by
Zscaler, or acts undertaken by third parties, including without
limitation denial of service attacks.

“Hardware” means the Zscaler-provided hardware used to connect to
the SaaS.

“Intellectual Property Rights” means copyrights (including, without
limitation, the exclusive right to use, reproduce, modify, distribute,
publicly display and publicly perform the copyrighted work), trademark
rights (including, without limitation, trade names, trademarks,
service marks, and trade dress), patent rights (including, without
limitation, the exclusive right to make, use and sell), trade
secrets, moral rights, right of publicity, authors’ rights, contract
and licensing rights, goodwill and all other intellectual property
rights as may exist now and/or hereafter come into existence and
all renewals and extensions thereof, in any state, country, or
jurisdiction.

“Order” means a written order form/sales proposal, purchase order,
or similar ordering document for Products submitted to, and approved
by, Zscaler and/or Partner.

“Partner” means the Zscaler-approved partner authorized by Zscaler
to resell or otherwise provide Products to end user customers.

“Personal Data” has the same meaning defined in the Data Processing
Agreement under Section 10.

“Products” means, collectively, the Zscaler SaaS, Software, Hardware,
Deployment Services, and Support Services, including all Upgrades.

“SaaS” means the subscription cloud-based service provided by Zscaler
for the Subscription Term set forth in the Order.

“SLAs” means the Service Level Agreements provided by Zscaler for
each applicable Product, described at
https://www.zscaler.com/legal/sla-support.

“Software” means any Zscaler software, utility, tool or other
computer or program code provided directly or indirectly to Customer
in object (binary) code only, as well as any copies (whether complete
or partial) made by or on Customer’s behalf. The term “Software”
also includes any updates, upgrades or other new features, functionality
or enhancements to the Software made available directly or indirectly
to Customer.

“Subscription Term” means the Initial Subscription Term and any and
all Renewal Subscription Terms (as defined in Section 7.2),
collectively.

“Support Services” means the support services provided by Zscaler
with respect to each applicable Product described at
https://www.zscaler.com/legal/sla-support.

“Systems Data” means the machine-generated data or metadata collected
or discovered by Zscaler while providing, maintaining, improving,
developing, and/or analyzing the Products but excluding Customer
Data or any Personal Data. Examples of Systems Data include, but
are not limited to, network telemetry data, policy enforcement data,
and product usage data.

“Threat Actor Data” means any data or metadata related to malware,
spyware, viruses, worms, Trojan horses, or other potentially malicious
or harmful code or files, URLs, DNS data, commands, processes,
techniques, or information security compromises or vulnerabilities
collected or discovered by Zscaler while providing, maintaining,
improving, developing, and/or analyzing the Products but excluding
Customer Data or any Personal Data .

“Upgrades” means all cloud-wide modifications, enhancements and
corrections to the Products made by Zscaler, including (i) corrections
of failures to conform to or to operate in accordance with the
Documentation; (ii) temporary and permanent error corrections
delivered as part of the Support Services; and (iii) all additions,
updates, new versions and releases, and new features, and changes
made by Zscaler in response to legal, technological or other
developments. For clarity, “Upgrades” does not include any additional
features or enhancements made available to customers by Zscaler for
an additional cost.

“Zscaler Materials” means all Zscaler proprietary materials, including
but not limited to Systems Data and Threat Actor Data, Intellectual
Property Rights for all Products and Documentation, Zscaler’s
processes and methods, and/or materials distributed by Zscaler
during any presentations, proof of concepts, or demonstrations of
the Products.

2. Orders

2.1 Governing Terms. Customer and its Affiliates may purchase
Products through an Order.  All Orders and Customer’s access and
use of the Products will be governed by the terms and conditions
in this Agreement and the Documentation.  Where a Customer Affiliate
is named in an Order, that Order will create a separate agreement
between that Affiliate and Zscaler, incorporating the terms of this
Agreement with the Affiliate deemed "Customer." For clarity, Zscaler
will not be obligated to provide any Products to Customer or its
Affiliate(s) until Zscaler receives a valid Order for such Products.

2.2 Non-Reliance.  Customer and its Affiliates agree that its
purchase of any Products is neither contingent upon the delivery
of any future functionality or features nor dependent upon any oral
or written comments made by Zscaler with respect to any future
functionality or features.

3. Payment

Unless otherwise agreed to in writing by the parties, Fees and
payment terms shall be agreed and documented between Customer and/or
its Affiliate(s) and the Partner.

4. Subscription Rights; Intellectual Property Rights; and Restrictions

4.1 Subscription Rights

Subject to the terms and conditions in this Agreement, Zscaler
grants Customer and its Affiliates a limited, non-transferable,
non-assignable (except as set forth in this Agreement), non-exclusive
right to access and use, and to permit their Authorized Users to
access and use the Products for the internal business purposes of
Customer and its Affiliates during the Subscription Term for the
quantity of purchased Products set forth in the Order.

4.2 Access and Use of Products

Customer agrees to only access and use the Products in accordance
with this Agreement and the applicable Documentation, including any
relevant Product usage guidelines. Customer and Zscaler agree to
work together in good faith to promptly resolve any unauthorized
access or use of the Products by Customer.

4.3 Ownership and Intellectual Property Rights

4.3.1 Zscaler

All rights and title in and to the Products, Zscaler Materials and
Documentation, including all Intellectual Property Rights inherent
therein, belong exclusively to Zscaler and its licensors.  No rights
are granted to Customer other than as expressly set forth in this
Agreement.

4.3.2 Customer

All rights and title in and to the Customer Data, including all
Intellectual Property Rights inherent therein, belong exclusively
to Customer.  No rights are granted to Zscaler other than as expressly
set forth in this Agreement.

4.4 Restrictions

Customer shall not and shall not allow or permit any third party
to: (i) modify, copy, display, republish or create derivative works
based on the Products or Zscaler Materials; (ii) reverse engineer
the Products; (iii) access or use the Products to build a competitive
product or service, or copy any ideas, features, functions or
graphics of the Products; (iv) use the Products in any way prohibited
by applicable law or that would cause either party to violate
applicable law including but not limited to: (1) sending spam or
other duplicative or unsolicited messages; (2) using the Products
to send infringing, obscene, threatening, libelous, or other unlawful
material; (3) using the Products to access blocked services; or (4)
uploading to the Products or using the Products to send or store
viruses, worms, time bombs, trojan horses or other harmful or
malicious code, files, scripts, agents or programs; (v) use the
Products to run automated queries to external websites (because
such websites may include Zscaler IP addresses in their respective
IP block lists); (vi) interfere with or disrupt the integrity or
performance of the Products or the data contained therein; (vii)
attempt to gain unauthorized access to the Products or its related
systems or networks; (viii) remove or alter any trademark, logo,
copyright or other proprietary notices, legends, symbols or labels
in the Products; (ix) perform penetration or load testing on the
Products or Zscaler’s cloud without the prior written consent of
Zscaler and agreeing to certain conditions and requirements for
such penetration or load testing; (x) without the express prior
written consent of Zscaler, conduct any public benchmarking or
comparative study or analysis involving the Products; and (xi)
access or use the Products from a prohibited location in violation
of U.S. trade and economic sanctions, including without limitation,
Cuba; Iran; North Korea; Syria; the so-called Donetsk People’s
Republic, the Luhansk People’s Republic, or Crimea Regions of
Ukraine; or any other country/region that becomes prohibited.

4.5 Customer Responsibilities

Customer agrees and understands that:

(a) it is responsible for all activity of Authorized Users and for
Authorized Users’ compliance with this Agreement; and it shall: (i)
have sole responsibility for the accuracy, quality, integrity,
legality, reliability and appropriateness of all Customer Data;
(ii) prevent unauthorized access to, or use of, the Products, and
notify Zscaler promptly of any such unauthorized access or use; and
(iii) comply with all applicable laws and/or regulations in using
the Products;

(b) Customer is solely responsible for its connection to the Internet
or any equipment or third party licenses necessary for Customer to
use the Products;

(c) in order for Zscaler to provide the SaaS, Customer is responsible
for forwarding its web traffic or internal traffic to Zscaler via
valid forwarding mechanisms that allow for automatic fail over (i.e.
PAC, IPSEC, GRE tunnels, and/or Zscaler App); and

(d) it is responsible for supplying Zscaler with any technical data
and other information and authorizations that Zscaler may reasonably
request to allow Zscaler to provide the Products to Customer.

4.6 Zscaler Rights

(a) Zscaler will only process and use Customer Data for the purpose
of fulfilling its obligations under this Agreement. To the extent
Customer Data contains Personal Data, the parties agree that the
obligations set forth under Section 10 apply with respect to such
Personal Data.

(b) Without limiting Zscaler’s confidentiality and/or security
obligations set forth in this Agreement, Zscaler reserves the right
to use or act upon any suggestions, ideas, enhancement requests,
feedback, recommendations, or other information provided by Customer
relating to the Products without restriction and without obligation
to Customer (collectively, “Feedback”). Zscaler acknowledges that
all Feedback is provided “as-is” without any warranty.

(c) Zscaler reserves the right to manage bandwidth or route traffic
across the Internet in a commercially optimal way, provided such
actions do not compromise Zscaler’s obligations under this Agreement.

(d) Zscaler reserves the right to suspend Customer’s access to or
download of Products in the event Customer’s use of the Products
represents an imminent threat to Zscaler’s network, or if directed
by a court or competent authority. In such cases, Zscaler will (i)
suspend such Products only to the extent reasonably necessary to
prevent any harm to Zscaler’s network (for example, blocking offending
source IP addresses); (ii) use its reasonable efforts to promptly
contact Customer and give Customer the opportunity to promptly
change the configuration of its server(s) accordingly and/or work
with Customer to promptly resolve the issues causing the suspension
of such Products; and (iii) reinstate any suspended Products
immediately after any issue is abated.

5. Warranties

5.1 Mutual Warranty

Each party represents and warrants that it has the legal power and
authority to enter into this Agreement.

5.2 SaaS and Software Warranty

Zscaler warrants that the SaaS and/or Software will (a) substantially
conform to the Documentation; and (b) be provided in accordance
with the SLAs.

5.3 Hardware Warranty

Zscaler warrants that the Hardware will perform substantially in
accordance with the applicable Documentation.

5.4 Deployment Services Warranty

Zscaler shall provide the Deployment Services and warrants that the
Deployment Services will be performed in a professional manner in
accordance with industry standards for like services.

5.5 Support Services and TAM Warranty

Zscaler shall provide the Support Services and warrants that the
Support Services will be performed in a professional manner in
accordance with industry standards for like services, but does not
guarantee that every question or problem will be resolved.  Zscaler’s
obligation to provide Support Services does not include services
requested as a result of causes or errors which are not attributable
to Zscaler or its authorized agents. If, upon investigating the
cause of the incident, Zscaler determines that there is a defect
in the Product, Zscaler will provide a remedy in the form of a
workaround, or another version of the Product that includes a bug
fix for the defect.  Customer agrees to provide reasonable support
information necessary to understand and resolve the incident, which
may include log files, configuration files and/or error messages.

5.6 Warranty Remedies

If Customer believes the warranty in Section 5.2 or 5.3 has been
breached, Customer must notify Zscaler of the breach no later than
thirty (30) days following the date the warranty was allegedly
breached, and Zscaler shall promptly correct the non-conformity at
its own expense if a breach of the relevant warranty occurred. If
Customer believes the warranty in Section 5.4 has been breached,
Customer must notify Zscaler of the breach no later than thirty
(30) days following the date the Deployment Services were provided,
and Zscaler shall promptly correct or re-perform the Deployment
Services if a breach of the warranty occurred. Without limiting any
Service Credits incurred in the event Zscaler does not meet the
SLAs, the remedies stated in this Section 5 are the sole remedies,
and Zscaler’s sole obligation, with respect to Products that fail
to comply with the foregoing warranties.

5.7 Disclaimer of Warranties

Except for the express warranties set forth herein, all Products
are provided on an “as is” basis. Zscaler expressly disclaims, to
the maximum extent permissible under applicable law, all warranties,
conditions, and other terms whether express, implied by common law,
statute or otherwise, including without limitation any implied
warranty of merchantability, fitness for a particular purpose,
accuracy, noninfringement, or that the Products will be error-free.

6. Confidential Information

6.1 Definition of Confidential Information

As used herein, “Confidential Information” means all confidential
and proprietary information of a party (“Disclosing Party”) disclosed
to the other party (“Receiving Party”), whether orally or in writing,
that is designated as confidential or that reasonably should be
understood to be confidential given the nature of the information
or the circumstances of disclosure, including, to the extent permitted
by applicable law, the terms and conditions of this Agreement
(including pricing and other terms reflected in all Orders hereunder),
the Customer Data, the Products, the Zscaler Materials, Zscaler’s
security information and reports, and each party’s respective
business and marketing plans, technology, and technical information,
product designs, and business processes.

6.2 Exceptions

The obligations in this Section 6 shall not apply to any information
that: (a) is or becomes generally known to the public without breach
of any obligation owed to the Disclosing Party; (b) was known to
the Receiving Party prior to its disclosure by the Disclosing Party
without breach of any obligation owed to the Disclosing Party and
without an obligation of confidentiality; (c) was independently
developed by the Receiving Party without the use of or reference
to the Confidential Information of the Disclosing Party; or (d) is
lawfully received from a third party without breach of any obligation
owed to the Disclosing Party and without an obligation of
confidentiality.

6.3 Confidentiality

The Receiving Party shall not disclose or use any Confidential
Information of the Disclosing Party for any purpose outside the
scope of this Agreement, except with the Disclosing Party’s prior
written permission. Either party may disclose Confidential Information
on a need-to-know basis to (a) its personnel, auditors and Affiliates
who are subject to the same confidentiality obligations, and (b)
its attorneys and accountants who are either subject to professional
obligations of confidentiality or have agreed to be bound by
confidentiality obligations at least as protective as those set out
herein.

6.4 Protection

The Receiving Party will use at least the same level of care to
prevent unauthorized use of the Disclosing Party’s Confidential
Information as it uses for its own Confidential Information, but
in no event less than a reasonable standard of care.

6.5 Compelled Disclosure

If the Receiving Party is compelled by law to disclose Confidential
Information of the Disclosing Party, it shall provide the Disclosing
Party with prior notice of such compelled disclosure, to the extent
legally permitted, and reasonable assistance, at Disclosing Party’s
cost, if the Disclosing Party wishes to contest the disclosure.

6.6 Remedies

If the Receiving Party discloses or uses (or threatens to disclose
or use) or the Disclosing Party reasonably believes that the Receiving
Party may disclose or use any Confidential Information of the
Disclosing Party in breach of the confidentiality protections
hereunder, or if the Receiving Party is compelled to disclose (or
is likely to become compelled to disclose) any Confidential Information
of the Disclosing Party pursuant to Section 6.5, the Disclosing
Party shall have the right, in addition to any other remedies
available to it, to seek injunctive relief to enjoin such acts or
seek a protective order regarding such acts.

7. Term and Termination

7.1 Agreement Term

This Agreement shall continue in effect for the Subscription Term.

7.2 Order Term

The initial term of Customer’s subscription to the Products will
begin on the start date set forth in an Order and will continue for
the period of time stated in the Order (“Initial Subscription Term”).
Prior to the end of the Initial Subscription Term, the length and
pricing for a renewal term may be agreed to (“Renewal Subscription
Term”); otherwise, Customer’s subscription will terminate at the
end of the Initial Subscription Term (or the then-applicable Renewal
Subscription Term).

7.3 Termination for Material Breach

Either party may terminate this Agreement and any Order: (a) if the
other party is in material breach of any of the terms and conditions
of this Agreement and does not cure such material breach within
thirty (30) days of receiving notice; or (b) if the other party
becomes the subject of a petition in bankruptcy or any proceeding
relating to insolvency, receivership, liquidation, or assignment
for the benefit of creditors.

7.4 Effect of Termination

The following provisions shall survive the termination of this
Agreement and all Orders:  Section 3 (Payment), Section 4 (Subscription
Rights; Intellectual Property Rights; and Restrictions;) other than
Section 4.1, Section 5.7 (Disclaimer of Warranties), Section 6
(Confidential Information), Section 7.4 (Effect of Termination),
Section 8 (Indemnity), Section 9 (Limitation of Liability), Section
10 (Data Privacy and Security), Section 11 (Export Control and
Anti-Corruption), and Section 12 (General Provisions).

8. Indemnity

8.1 Zscaler Indemnity

Zscaler shall defend Customer against any third-party claim alleging
Customer’s use of the Products as permitted hereunder infringes or
misappropriates that third party’s valid United States, European
Union, or United Kingdom patent, copyright, trademark or trade
secret, and Zscaler shall, subject to Section 9, at its expense,
indemnify Customer for any damages finally awarded against Customer
(including reasonable legal fees) or any amounts agreed in settlement
by Zscaler in connection with such claim.

8.2 Response to Claims

If the Products, or parts thereof, become, or in Zscaler’s opinion
may become, the subject of an infringement claim, Zscaler may, at
its option: (a) procure for Customer the right to continue using
the Products as set forth herein; (b) replace or modify the Products
to make it non-infringing, provided that such replacement or
modification does not compromise Zscaler’s obligations under this
Agreement; or (c) if options (a) or (b) are not commercially and
reasonably practicable as determined by Zscaler, terminate this
Agreement and the applicable Order and refund Customer, on a prorated
basis, any pre-paid Fees for the corresponding unused portion of
the Subscription Term.

8.3 Indemnity Exclusions

Zscaler will have no liability or obligation under this Section
with respect to any claim if such claim is caused in whole or in
part by: (a) Customer’s use of a Product not in accordance with the
Documentation; (b) modification of a Product by anyone other than
Zscaler; (c) the combination, operation, or use of any Product with
any third party hardware or software where the Product would not
by itself be infringing absent such combination, operation, or use;
or (d) Customer’s continued use of an infringing version of the
Products after Zscaler has provided a non-infringing version and
notified Customer of its availability.

8.4 Customer Indemnity

Customer will defend and indemnify Zscaler from and against any
claim brought by a third party against Zscaler arising from or
related to Customer’s violation of Section 4.4 of this Agreement.

8.5 Indemnity Procedure

The indemnification obligations in this Section shall be subject
to the indemnified party:  (a) promptly notifying the indemnifying
party in writing upon receiving notice of any threat or claim of
such action; (b) giving the indemnifying party exclusive control
and authority over the defense and/or settlement of such claim
(provided any such settlement unconditionally releases the indemnified
party of all liability); and (c) providing reasonable assistance
requested by the indemnifying party, at the indemnifying party’s
expense.

8.6 THIS SECTION 8 STATES ZSCALER’S ENTIRE LIABILITY AND CUSTOMER’S
SOLE REMEDY WITH RESPECT TO ANY INFRINGEMENT OF INTELLECTUAL PROPERTY
RIGHTS BY THE PRODUCTS.

9. Limitation of Liability

9.1 Waiver of Consequential Damages

In no event will either party be liable for any lost profits, revenue
or savings; lost business opportunities or data; substitute goods
or services; or indirect, special, incidental, consequential,
punitive or exemplary damages of any kind (including damage to
business reputation or goodwill).

9.2 Limitation of Monetary Damages

Each party’s aggregate liability for all claims arising out of this
Agreement and all Orders shall be limited to the total fees paid
and/or payable to Zscaler for the applicable Product giving rise
to the liability during the twelve (12) months immediately preceding
the first incident giving rise to the first claim.

9.3 Form of Action

The exclusions and limitations in this Section 9 apply (a) to the
maximum extent permitted by applicable law; and (b) whatever the
form of action, whether for breach of contract, misrepresentations,
negligence, strict liability, other torts or otherwise, even if the
other party has been told in advance of the possibility of such
damages or losses or such damages or losses were reasonably
foreseeable.

10. Data Privacy and Security

10.1 Data Privacy

The parties shall comply with the privacy and security terms of the
Data Processing Agreement at
https://www.zscaler.com/legal/data-processing-agreement which is
incorporated into this Agreement by reference.

10.2 Security

Zscaler maintains reasonable and appropriate physical, organizational,
administrative, and technical safeguards designed to protect Customer
Data from loss, misuses, unauthorized access, disclosure, alteration,
and destruction as set out at
https://www.zscaler.com/legal/security-measures (“Security Measures”).
Zscaler is certified under ISO 27001 and System and Organization
Controls (SOC) 2, Type II standards and is audited annually by an
independent third party to ensure its ongoing compliance with these
certifications. Zscaler regularly tests, assesses and evaluates the
effectiveness of the Security Measures. Upon written request, Zscaler
agrees to provide Customer with a copy of its most recent ISO 27001
certificate and/or SOC 2, Type II report. Zscaler will not materially
decrease the Security Measures during the Subscription Term. Zscaler
will take appropriate steps to ensure compliance with the Security
Measures by its employees, contractors and subcontractors/sub-processors
to the extent applicable to their scope of performance.

11. Export Compliance and Anti-Corruption

11.1 Export Compliance

The Products and other software or components of the Products which
Zscaler may provide or make available to Customer may be subject
to United States export control and economic sanctions laws and
other foreign trade controls.  The parties represent that they are
in compliance with applicable United States and foreign export
controls, economic sanctions, and other trade controls.

11.2 Anti-Corruption

In performing this Agreement, the parties agree to comply at all
times with the applicable laws related to money-laundering, bribery,
and anti-corruption, including the Foreign Corrupt Practices Act
of 1977, the UK Anti-bribery Act of 2010, and any other applicable
anti-corruption legislations (“Anti-corruption Laws”). Each party
represents that it has not received or been offered any illegal or
improper bribe, kickback, payment, gift, or thing of value from an
employee or agent of the other party in connection with this
Agreement. Reasonable gifts and entertainment provided in the
ordinary course of business do not violate the above restriction.

12. General Provisions

12.1 Relationship of the Parties

The parties are independent contractors.  This Agreement does not
create a partnership, franchise, joint venture, agency, fiduciary,
or employment relationship between the parties.

12.2 Notices

Except as otherwise provided herein, all notices shall be in writing
and deemed given upon: (i) personal delivery; (ii) when received
by the addressee if sent by a recognized overnight courier (receipt
requested); (iii) the second business day after mailing; or (iv)
the first business day after sending by email, except that email
shall not be sufficient for notices regarding a legal claim. Notices
shall be sent to the parties at the address set forth in this
Agreement (or as subsequently updated in writing) or Customer’s
current business contact if known, with a <NAME_EMAIL>
for Zscaler and/or Customer’s legal department.

12.3 Waiver and Cumulative Remedies

No failure or delay by either party in exercising any right under
this Agreement shall constitute a waiver of that right.  Other than
as expressly stated herein, the remedies provided herein are in
addition to, and not exclusive of, any other remedies of a party
at law or in equity.

12.4 Severability

If any provision of this Agreement is held by a court of competent
jurisdiction to be contrary to law, the provision shall be modified
by the court and interpreted so as best to accomplish the objectives
of the original provision to the fullest extent permitted by law,
and the remaining provisions of this Agreement shall remain in full
force and effect.

12.5 Assignment

Neither party may assign any of its rights or obligations hereunder,
whether by operation of law or otherwise, without the prior written
consent of the other party (not to be unreasonably withheld), except
that either party may assign this Agreement in its entirety, without
the consent of the other party, to (a) an Affiliate; or (b) in
connection with a merger, acquisition, corporate reorganization,
or sale of all or substantially all of its assets not involving a
direct competitor of the other party.  Any attempt by a party to
assign its rights or obligations under this Agreement in breach of
this Section 12.5 shall be void and of no effect.  Subject to the
foregoing, this Agreement shall bind and inure to the benefit of
the parties, their respective successors and permitted assigns.

12.6 Governing Law and Jurisdiction

This Agreement and any disputes arising out of or related hereto
shall be governed by and construed in accordance with the laws of
the State of California, without giving effect to its conflicts of
laws rules, and will be subject to the exclusive jurisdiction of
the federal and state courts located in Santa Clara County.
Notwithstanding the foregoing, each party reserves the right to
file a suit or action in any court of competent jurisdiction as
such party deems necessary to protect its Intellectual Property
Rights or other proprietary rights. The United Nations Convention
on the International Sale of Goods and the Uniform Computer Information
Transactions Act do not apply to this Agreement.

12.7 Force Majeure

Neither party shall be liable for delay or non-performance of its
obligations hereunder (or part thereof) if the cause of delay or
non-performance is due to a Force Majeure Event.  The party affected
shall be relieved from its obligations (or part thereof) for the
time that the Force Majeure Event lasts and hinders the performance
of said obligations (or part thereof).  The party affected shall
promptly notify the other party and make reasonable efforts to
mitigate the effects of the Force Majeure Event.

12.8 Evaluation Products

From time to time, Zscaler may make available certain Zscaler
products for proof of concept, beta testing, interactive demo, or
other similar evaluative purposes (the “Evaluation Products”).
Customer shall only access and use the Evaluation Products for
internal evaluation purposes for a mutually agreed period (the
“Evaluation Period”).  At the end of the Evaluation Period, Customer
Data will be deleted pursuant to Zscaler’s standard retention and
deletion periods, unless otherwise agreed to by the parties.  For
any Evaluation Products, only Sections 4, 5.7, 6, 9, 10, 11, 12,
and the applicable definitions in Section 1 of this Agreement shall
apply, with the Evaluation Products being deemed as “Products” in
those sections.

12.9 Entire Agreement

This Agreement constitutes the entire agreement between the parties,
and supersedes all prior and contemporaneous agreements, proposals
or representations, written or oral, concerning its subject matter.
The parties are not relying and have not relied on any representations
or warranties whatsoever regarding the subject matter of this
Agreement, express or implied, except for the representations and
warranties set forth in this Agreement.

12.10 Variations

No modification, amendment, or waiver of any provision of this
Agreement shall be effective unless in writing and signed by the
party against whom the modification, amendment or waiver is to be
asserted.  Any terms, conditions or other provisions set forth on
any Customer purchase order, procurement portal, request for proposal
(or other questionnaire), preprinted form, or other non-Zscaler
ordering document are hereby rejected by Zscaler and shall be null
and void.
expected   CRC32 $C416E5D7
/dev/disk6          	GUID_partition_scheme          	
/dev/disk6s1        	Apple_APFS                     	
/dev/disk7          	EF57347C-0000-11AA-AA11-0030654	
/dev/disk7s1        	*************-11AA-AA11-0030654	/Volumes/Zscaler Zero Trust Browser Installer 1

2025-07-30 20:13:41,225 - dmg_mount_test.log - DEBUG - mount_dmg:132 - hdiutil stderr: 
2025-07-30 20:13:41,225 - dmg_mount_test.log - INFO - mount_dmg:136 - DMG mounted successfully at: /Volumes/Zscaler Zero Trust Browser Installer 1
2025-07-30 20:13:41,226 - dmg_mount_test.log - INFO - unmount_dmg:301 - Unmounting Agent: /Volumes/Zscaler Zero Trust Browser Installer 1
2025-07-30 20:13:41,570 - dmg_mount_test.log - INFO - unmount_dmg:315 - Successfully unmounted: /Volumes/Zscaler Zero Trust Browser Installer 1

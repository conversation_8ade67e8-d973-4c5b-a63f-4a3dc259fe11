2025-07-30 20:13:41,918 - quiet_install_test.log - INFO - initialize_logger:60 - Logger initialized: quiet_install_test.log
2025-07-30 20:13:41,918 - quiet_install_test.log - INFO - initialize_logger:61 - Log file: /Users/<USER>/Z-Repos/ztb_automation/Reports/logs/quiet_install_test.log_20250730_201341.log
2025-07-30 20:13:41,918 - quiet_install_test.log - INFO - __init__:51 - ZTB Installer initialized
2025-07-30 20:13:41,918 - quiet_install_test.log - INFO - mount_dmg:107 - Mounting DMG file: /Users/<USER>/Z-Repos/ztb_automation/OS_Mac/resource/ZscalerZTBSetup-arm64-0.0.1.dmg
2025-07-30 20:13:41,999 - quiet_install_test.log - DEBUG - mount_dmg:131 - hdiutil stdout: /dev/disk6          	GUID_partition_scheme          	
/dev/disk6s1        	Apple_APFS                     	
/dev/disk7          	EF57347C-0000-11AA-AA11-0030654	
/dev/disk7s1        	*************-11AA-AA11-0030654	/Volumes/Zscaler Zero Trust Browser Installer 1

2025-07-30 20:13:41,999 - quiet_install_test.log - DEBUG - mount_dmg:132 - hdiutil stderr: 
2025-07-30 20:13:41,999 - quiet_install_test.log - INFO - mount_dmg:136 - DMG mounted successfully at: /Volumes/Zscaler Zero Trust Browser Installer 1
